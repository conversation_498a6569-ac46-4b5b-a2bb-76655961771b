/**
 * 動態商品載入器
 * 從後端 API 載入商品資料並動態生成商品選擇介面
 */

function ProductLoader() {
    const [products, setProducts] = React.useState([]);
    const [loading, setLoading] = React.useState(true);
    const [error, setError] = React.useState(null);

    // 載入商品資料
    React.useEffect(() => {
        loadProducts();
    }, []);

    const loadProducts = async () => {
        try {
            setLoading(true);
            setError(null);
            
            const response = await fetch('./api/products_manager.php?path=frontend');
            const result = await response.json();
            
            if (result.success) {
                // 只顯示啟用且有庫存的商品
                const availableProducts = result.data.filter(product => 
                    product.is_active && product.stock_status !== 'sold_out'
                );
                setProducts(availableProducts);
            } else {
                setError('載入商品失敗: ' + result.message);
            }
        } catch (err) {
            setError('載入商品時發生錯誤: ' + err.message);
            console.error('Product loading error:', err);
        } finally {
            setLoading(false);
        }
    };

    // 獲取商品狀態樣式
    const getProductStyle = (product) => {
        switch (product.stock_status) {
            case 'sold_out':
                return 'bg-gray-100 opacity-60';
            case 'limited':
                return 'border-orange-300 bg-orange-50';
            default:
                return '';
        }
    };

    // 獲取商品狀態文字
    const getStatusText = (product) => {
        switch (product.stock_status) {
            case 'sold_out':
                return '本期已完售';
            case 'limited':
                return '庫存有限';
            default:
                return '';
        }
    };

    // 渲染商品項目
    const renderProduct = (product, quantity, updateQuantity) => {
        const isDisabled = product.stock_status === 'sold_out';
        const productStyle = getProductStyle(product);
        const statusText = getStatusText(product);

        return (
            <div key={product.id} className={`flex items-center justify-between p-3 border rounded-lg ${productStyle}`}>
                <div className="flex-1">
                    <span className={`font-medium ${isDisabled ? 'text-gray-500 line-through' : 'text-gray-700'}`}>
                        {product.name} (NT$ {product.price}/條)
                    </span>
                    {statusText && (
                        <div className={`text-sm font-bold mt-1 ${product.stock_status === 'sold_out' ? 'text-red-500' : 'text-orange-500'}`}>
                            {statusText}
                        </div>
                    )}
                    {product.stock_status === 'limited' && product.stock_quantity > 0 && (
                        <div className="text-xs text-orange-600 mt-1">
                            剩餘 {product.stock_quantity} 條
                        </div>
                    )}
                </div>
                <div className="flex items-center space-x-2 flex-shrink-0">
                    <button
                        type="button"
                        onClick={() => updateQuantity(product.id, -1)}
                        disabled={isDisabled}
                        className={`w-6 h-6 rounded-full flex items-center justify-center text-sm font-bold transition-colors ${
                            isDisabled 
                                ? 'bg-gray-300 cursor-not-allowed' 
                                : 'bg-gray-200 hover:bg-gray-300'
                        }`}
                    >
                        -
                    </button>
                    <span className={`w-6 text-center font-semibold text-sm ${isDisabled ? 'text-gray-400' : ''}`}>
                        {isDisabled ? 0 : quantity}
                    </span>
                    <button
                        type="button"
                        onClick={() => updateQuantity(product.id, 1)}
                        disabled={isDisabled || (product.stock_status === 'limited' && quantity >= product.stock_quantity)}
                        className={`w-6 h-6 rounded-full flex items-center justify-center text-sm font-bold transition-colors ${
                            isDisabled || (product.stock_status === 'limited' && quantity >= product.stock_quantity)
                                ? 'bg-gray-300 cursor-not-allowed' 
                                : 'bg-red-500 hover:bg-red-600 text-white'
                        }`}
                    >
                        +
                    </button>
                </div>
            </div>
        );
    };

    return {
        products,
        loading,
        error,
        renderProduct,
        reload: loadProducts
    };
}

/**
 * 動態商品選擇組件
 * 使用 ProductLoader 載入商品並提供選擇介面
 */
function DynamicProductSelection({ formData, setFormData }) {
    const { products, loading, error, renderProduct, reload } = ProductLoader();

    // 更新商品數量
    const updateProductQuantity = (productId, change) => {
        const currentQty = parseInt(formData.products[productId]) || 0;
        const newQty = Math.max(0, currentQty + change);
        
        // 檢查庫存限制
        const product = products.find(p => p.id === productId);
        if (product && product.stock_status === 'limited' && newQty > product.stock_quantity) {
            alert(`⚠️ 庫存不足，最多只能選擇 ${product.stock_quantity} 條`);
            return;
        }
        
        setFormData(prev => ({
            ...prev,
            products: { 
                ...prev.products, 
                [productId]: newQty 
            }
        }));
    };

    // 計算總金額
    React.useEffect(() => {
        if (products.length > 0) {
            let subtotal = 0;
            
            products.forEach(product => {
                const quantity = parseInt(formData.products[product.id]) || 0;
                subtotal += quantity * product.price;
            });

            let shipping = 0;
            if (subtotal > 0 && subtotal < 350) {
                shipping = 100;
            }

            setFormData(prev => ({
                ...prev,
                shipping,
                totalAmount: subtotal + shipping
            }));
        }
    }, [formData.products, products]);

    if (loading) {
        return (
            <div className="space-y-4" data-name="product-selection">
                <div className="flex items-center justify-center p-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-500"></div>
                    <span className="ml-2 text-gray-600">載入商品中...</span>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="space-y-4" data-name="product-selection">
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                    <div className="flex items-center justify-between">
                        <div>
                            <h3 className="text-red-800 font-medium">載入商品失敗</h3>
                            <p className="text-red-600 text-sm mt-1">{error}</p>
                        </div>
                        <button
                            onClick={reload}
                            className="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded text-sm transition-colors"
                        >
                            重試
                        </button>
                    </div>
                </div>
            </div>
        );
    }

    if (products.length === 0) {
        return (
            <div className="space-y-4" data-name="product-selection">
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-8 text-center">
                    <p className="text-gray-600">目前沒有可選擇的商品</p>
                    <button
                        onClick={reload}
                        className="mt-2 bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm transition-colors"
                    >
                        重新載入
                    </button>
                </div>
            </div>
        );
    }

    return (
        <div className="space-y-4" data-name="product-selection">
            {products.map(product => 
                renderProduct(
                    product, 
                    formData.products[product.id] || 0, 
                    updateProductQuantity
                )
            )}
            
            {/* 重新載入按鈕 */}
            <div className="text-center pt-2">
                <button
                    type="button"
                    onClick={reload}
                    className="text-sm text-gray-500 hover:text-gray-700 underline"
                >
                    🔄 重新載入商品
                </button>
            </div>
        </div>
    );
}

// 導出給其他組件使用
window.ProductLoader = ProductLoader;
window.DynamicProductSelection = DynamicProductSelection;
