<?php
/**
 * 商品管理 API
 * 提供商品的 CRUD 操作，支援庫存管理、價格設定等功能
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 處理 OPTIONS 請求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 錯誤報告設定
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

class ProductsManager {
    private $dataFile;
    private $products;

    public function __construct() {
        $this->dataFile = __DIR__ . '/config/products.json';
        $this->loadProducts();
    }

    /**
     * 載入商品資料
     */
    private function loadProducts() {
        if (file_exists($this->dataFile)) {
            $content = file_get_contents($this->dataFile);
            $this->products = json_decode($content, true) ?: [];
        } else {
            // 初始化預設商品資料
            $this->products = [
                [
                    'id' => 'radish',
                    'name' => '原味蘿蔔糕',
                    'description' => '使用新鮮蘿蔔與優質米漿精心製作，保留蘿蔔的自然甜味和香氣，口感鬆軟Q彈。',
                    'price' => 250,
                    'image' => '/images/radish-cake.jpg',
                    'ingredients' => ['新鮮蘿蔔', '在來米漿', '香菇', '蔥', '素料'],
                    'weight' => '600g',
                    'is_vegetarian' => true,
                    'stock_status' => 'available', // available, limited, sold_out
                    'stock_quantity' => 100,
                    'category' => 'traditional',
                    'sort_order' => 1,
                    'is_active' => true,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ],
                [
                    'id' => 'taro',
                    'name' => '芋頭粿',
                    'description' => '精選優質芋頭，口感綿密香甜，是傳統台式點心的經典選擇。',
                    'price' => 350,
                    'image' => '/images/taro-cake.jpg',
                    'ingredients' => ['新鮮芋頭', '在來米漿', '香菇', '蔥'],
                    'weight' => '600g',
                    'is_vegetarian' => true,
                    'stock_status' => 'available',
                    'stock_quantity' => 50,
                    'category' => 'premium',
                    'sort_order' => 2,
                    'is_active' => true,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ],
                [
                    'id' => 'hongkong',
                    'name' => '台式鹹蘿蔔糕',
                    'description' => '採用頂級食材，添加臘肉、蝦米等配料，風味更加豐富，口感紮實，香氣十足。',
                    'price' => 350,
                    'image' => '/images/hongkong-style.jpg',
                    'ingredients' => ['新鮮蘿蔔', '在來米漿', '臘肉', '蝦米', '香菇', '蔥'],
                    'weight' => '600g',
                    'is_vegetarian' => false,
                    'stock_status' => 'sold_out', // 設為完售
                    'stock_quantity' => 0,
                    'category' => 'premium',
                    'sort_order' => 3,
                    'is_active' => true,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]
            ];
            $this->saveProducts();
        }
    }

    /**
     * 儲存商品資料
     */
    private function saveProducts() {
        $configDir = dirname($this->dataFile);
        if (!is_dir($configDir)) {
            mkdir($configDir, 0755, true);
        }
        
        return file_put_contents($this->dataFile, json_encode($this->products, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    }

    /**
     * 獲取所有商品
     */
    public function getAllProducts($includeInactive = false) {
        $products = $this->products;
        
        if (!$includeInactive) {
            $products = array_filter($products, function($product) {
                return $product['is_active'] ?? true;
            });
        }
        
        // 按排序順序排列
        usort($products, function($a, $b) {
            return ($a['sort_order'] ?? 999) - ($b['sort_order'] ?? 999);
        });
        
        return [
            'success' => true,
            'data' => $products,
            'count' => count($products)
        ];
    }

    /**
     * 獲取單一商品
     */
    public function getProduct($id) {
        foreach ($this->products as $product) {
            if ($product['id'] === $id) {
                return [
                    'success' => true,
                    'data' => $product
                ];
            }
        }
        
        return [
            'success' => false,
            'message' => '商品不存在'
        ];
    }

    /**
     * 新增商品
     */
    public function createProduct($data) {
        // 驗證必要欄位
        $required = ['id', 'name', 'price'];
        foreach ($required as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                return [
                    'success' => false,
                    'message' => "缺少必要欄位: {$field}"
                ];
            }
        }

        // 檢查 ID 是否已存在
        foreach ($this->products as $product) {
            if ($product['id'] === $data['id']) {
                return [
                    'success' => false,
                    'message' => '商品 ID 已存在'
                ];
            }
        }

        // 設定預設值
        $newProduct = array_merge([
            'description' => '',
            'image' => '',
            'ingredients' => [],
            'weight' => '',
            'is_vegetarian' => false,
            'stock_status' => 'available',
            'stock_quantity' => 0,
            'category' => 'general',
            'sort_order' => count($this->products) + 1,
            'is_active' => true,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ], $data);

        $this->products[] = $newProduct;
        
        if ($this->saveProducts()) {
            return [
                'success' => true,
                'message' => '商品新增成功',
                'data' => $newProduct
            ];
        } else {
            return [
                'success' => false,
                'message' => '儲存失敗'
            ];
        }
    }

    /**
     * 更新商品
     */
    public function updateProduct($id, $data) {
        foreach ($this->products as &$product) {
            if ($product['id'] === $id) {
                // 更新時間戳
                $data['updated_at'] = date('Y-m-d H:i:s');
                
                // 合併資料
                $product = array_merge($product, $data);
                
                if ($this->saveProducts()) {
                    return [
                        'success' => true,
                        'message' => '商品更新成功',
                        'data' => $product
                    ];
                } else {
                    return [
                        'success' => false,
                        'message' => '儲存失敗'
                    ];
                }
            }
        }
        
        return [
            'success' => false,
            'message' => '商品不存在'
        ];
    }

    /**
     * 刪除商品（軟刪除）
     */
    public function deleteProduct($id) {
        return $this->updateProduct($id, ['is_active' => false]);
    }

    /**
     * 更新庫存狀態
     */
    public function updateStock($id, $status, $quantity = null) {
        $validStatuses = ['available', 'limited', 'sold_out'];
        if (!in_array($status, $validStatuses)) {
            return [
                'success' => false,
                'message' => '無效的庫存狀態'
            ];
        }

        $updateData = ['stock_status' => $status];
        if ($quantity !== null) {
            $updateData['stock_quantity'] = max(0, (int)$quantity);
        }

        return $this->updateProduct($id, $updateData);
    }

    /**
     * 獲取前端使用的商品資料格式
     */
    public function getProductsForFrontend() {
        $products = $this->getAllProducts()['data'];
        
        // 轉換為前端需要的格式
        $frontendProducts = [];
        foreach ($products as $product) {
            $frontendProducts[] = [
                'id' => $product['id'],
                'name' => $product['name'],
                'price' => $product['price'],
                'stock_status' => $product['stock_status'],
                'stock_quantity' => $product['stock_quantity'],
                'is_active' => $product['is_active'],
                'sort_order' => $product['sort_order']
            ];
        }
        
        return [
            'success' => true,
            'data' => $frontendProducts
        ];
    }
}

// API 端點處理
try {
    $manager = new ProductsManager();
    $method = $_SERVER['REQUEST_METHOD'];
    $path = $_GET['path'] ?? '';

    switch ($method) {
        case 'GET':
            if (empty($path)) {
                // 獲取所有商品
                $includeInactive = isset($_GET['include_inactive']) && $_GET['include_inactive'] === 'true';
                $result = $manager->getAllProducts($includeInactive);
            } elseif ($path === 'frontend') {
                // 獲取前端格式的商品資料
                $result = $manager->getProductsForFrontend();
            } else {
                // 獲取單一商品
                $result = $manager->getProduct($path);
            }
            break;

        case 'POST':
            $input = file_get_contents('php://input');
            $data = json_decode($input, true);
            
            if (empty($data)) {
                $data = $_POST;
            }
            
            $result = $manager->createProduct($data);
            break;

        case 'PUT':
            $input = file_get_contents('php://input');
            $data = json_decode($input, true);
            
            if (empty($data)) {
                $data = $_POST;
            }
            
            if (isset($_GET['action']) && $_GET['action'] === 'stock') {
                // 更新庫存
                $status = $data['status'] ?? '';
                $quantity = $data['quantity'] ?? null;
                $result = $manager->updateStock($path, $status, $quantity);
            } else {
                // 一般更新
                $result = $manager->updateProduct($path, $data);
            }
            break;

        case 'DELETE':
            $result = $manager->deleteProduct($path);
            break;

        default:
            http_response_code(405);
            $result = [
                'success' => false,
                'message' => '不支援的請求方法'
            ];
            break;
    }

    echo json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => '伺服器錯誤: ' . $e->getMessage()
    ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
}
?>
