<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>融氏古早味手工蘿蔔糕 - 傳統美味，用心製作</title>
    <meta name="description" content="融氏古早味手工蘿蔔糕，堅持純手工製作，使用在地新鮮食材，提供古早味蘿蔔糕、芋頭糕、台式鹹蘿蔔糕等多種美味選擇。">
    <meta name="keywords" content="蘿蔔糕,芋頭糕,台式鹹蘿蔔糕,手工製作,雲林小吃,傳統美食">

    <meta property="og:title" content="融氏古早味手工蘿蔔糕 - 傳統美味，用心製作">
    <meta property="og:description" content="堅持純手工製作，使用在地新鮮食材，提供古早味蘿蔔糕、芋頭糕、台式鹹蘿蔔糕等美味選擇。純米漿製作，手工用心，美味蘿蔔糕，立即訂購！">
    <meta property="og:image" content="https://767780.xyz/images/IMG_3825.jpg.webp">
    <meta property="og:type" content="website">

    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="融氏古早味手工蘿蔔糕 - 傳統美味，用心製作">
    <meta name="twitter:description" content="融氏古早味手工蘿蔔糕，堅持純手工製作，使用在地新鮮食材，提供古早味蘿蔔糕、芋頭糕、台式鹹蘿蔔糕等多種美味選擇。">

    <!-- React 和相關庫 - 確保載入順序 -->
    <script crossorigin src="https://unpkg.com/react@18/umd/react.production.min.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.production.min.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    
    <!-- TailwindCSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link href="styles/main.css" rel="stylesheet">
    <link href="styles/header.css" rel="stylesheet">
    <link href="styles/hero.css" rel="stylesheet">
    <link href="styles/brand-story.css" rel="stylesheet">
    <link href="styles/products.css" rel="stylesheet">
    <link href="styles/process.css" rel="stylesheet">
    <link href="styles/reviews.css" rel="stylesheet">
    <link href="styles/order.css" rel="stylesheet">
    <link href="styles/footer.css" rel="stylesheet">
    <link href="styles/admin.css" rel="stylesheet">
    <link href="styles/floating-buttons.css" rel="stylesheet">
    <link href="styles/delivery-notice-modal.css" rel="stylesheet">
    <link href="styles/tailwind-compiled.css" rel="stylesheet">
    <link rel="icon" href="https://app.trickle.so/storage/app/LOGO-2.webp">
    
    <!-- LINE 瀏覽器相容性樣式 -->
    <style>
        /* LINE 瀏覽器特殊處理 */
        body {
            -webkit-overflow-scrolling: touch;
            overflow-x: hidden;
        }
        
        /* 載入指示器 */
        .loading-indicator {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: white;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            flex-direction: column;
        }
        
        .loading-spinner {
            border: 3px solid #f3f4f6;
            border-top: 3px solid #ef4444;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin-bottom: 16px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .loading-text {
            color: #6b7280;
            font-size: 14px;
        }
        
        /* 隱藏載入指示器 */
        .loading-indicator.hidden {
            display: none;
        }
    </style>
</head>

<body>
    <!-- 載入指示器 -->
    <div id="loadingIndicator" class="loading-indicator">
        <div class="loading-spinner"></div>
        <div class="loading-text">載入中...</div>
    </div>
    
    <div id="root"></div>
    
    <!-- LINE LIFF SDK - 條件載入 -->
    <script>
        // 檢測是否在 LINE 環境中
        const isLineEnvironment = /Line/i.test(navigator.userAgent) || 
                                 window.location.href.includes('liff://') ||
                                 window.location.search.includes('liff_id');
        
        console.log('🔍 環境檢測:', {
            userAgent: navigator.userAgent,
            isLineEnvironment: isLineEnvironment,
            url: window.location.href
        });
        
        // 只在 LINE 環境中載入 LIFF SDK
        if (isLineEnvironment) {
            console.log('📱 檢測到 LINE 環境，載入 LIFF SDK');
            const liffScript = document.createElement('script');
            liffScript.charset = 'utf-8';
            liffScript.src = 'https://static.line-scdn.net/liff/edge/2/sdk.js';
            liffScript.onload = function() {
                console.log('✅ LIFF SDK 載入完成');
                window.liffLoaded = true;
            };
            liffScript.onerror = function() {
                console.warn('⚠️ LIFF SDK 載入失敗，繼續使用一般模式');
                window.liffLoaded = false;
            };
            document.head.appendChild(liffScript);
        } else {
            console.log('🌐 一般瀏覽器環境，跳過 LIFF SDK');
            window.liffLoaded = false;
        }
    </script>
    
    <!-- 工具函數 -->
    <script src="utils/errorUtils.js"></script>
    <script src="utils/stagewise.js"></script>
    
    <!-- 優先載入 cityDistricts 和工具函數 -->
    <script src="dist/cityDistricts.js"></script>
    <script src="dist/orderUtils.js"></script>
    <script src="dist/sheetsUtils.js"></script>

    <!-- React 組件 - 確保按順序載入 -->
    <script type="text/babel" src="components/Header.js"></script>
    <script type="text/babel" src="components/ProcessSection.js"></script>
    <script type="text/babel" src="components/Hero.js"></script>
    <script type="text/babel" src="components/BrandStory.js"></script>
    <script type="text/babel" src="components/Products.js"></script>
    <script type="text/babel" src="components/ProductCard.js"></script>
    <script type="text/babel" src="components/CustomerReviews.js"></script>
    <script type="text/babel" src="components/MediaSection.js"></script>
    <script type="text/babel" src="components/Storage.js"></script>
    <script type="text/babel" src="components/OrderInstructions.js"></script>
    <script type="text/babel" src="components/OrderGuide.js"></script>
    <!-- 使用修正後的編譯版本 OrderForm -->
    <script src="dist/OrderForm.js?v=1.6"></script>
    <script type="text/babel" src="components/Footer.js?v=1.4"></script>
    <script type="text/babel" src="components/FloatingButtons.js"></script>
    <script type="text/babel" src="components/DeliveryNoticeModal.js"></script>
    <script type="text/babel" src="components/admin/Login.js"></script>
    <script type="text/babel" src="components/admin/AdminDashboard.js"></script>
    <script type="text/babel" src="utils/adminUtils.js"></script>
    
    <!-- 主應用程式 - 最後載入 -->
    <script type="text/babel">
        // LINE 環境相容性增強版 App
        const { useState, useEffect } = React;
        
        function App() {
            const [isLoading, setIsLoading] = useState(true);
            const [error, setError] = useState(null);
            const [liffReady, setLiffReady] = useState(false);
            const [currentPage, setCurrentPage] = useState('home');
            const [showDeliveryNotice, setShowDeliveryNotice] = useState(false);

            useEffect(() => {
                initializeApp();
            }, []);

            useEffect(() => {
                // 路由處理
                const handleHashChange = () => {
                    const hash = window.location.hash.slice(1);
                    setCurrentPage(hash || 'home');
                };

                window.addEventListener('hashchange', handleHashChange);
                handleHashChange();

                return () => window.removeEventListener('hashchange', handleHashChange);
            }, []);

            useEffect(() => {
                // 頁面載入完成後顯示配送提醒彈窗（僅在非管理後台頁面）
                if (currentPage !== 'admin' && !isLoading) {
                    const timer = setTimeout(() => {
                        setShowDeliveryNotice(true);
                        console.log('🔔 顯示配送提醒彈窗');
                    }, 1500); // 延遲1.5秒顯示，讓頁面先完全載入

                    return () => clearTimeout(timer);
                }
            }, [currentPage, isLoading]);
            
            const initializeApp = async () => {
                try {
                    console.log('🚀 開始初始化應用程式');
                    
                    // 等待 React 和其他依賴完全載入
                    await new Promise(resolve => {
                        if (window.React && window.ReactDOM) {
                            resolve();
                        } else {
                            setTimeout(resolve, 100);
                        }
                    });
                    
                    // 如果在 LINE 環境中，嘗試初始化 LIFF
                    if (isLineEnvironment) {
                        await initializeLIFF();
                    } else {
                        setLiffReady(true);
                    }
                    
                    // 隱藏載入指示器
                    const loadingIndicator = document.getElementById('loadingIndicator');
                    if (loadingIndicator) {
                        loadingIndicator.classList.add('hidden');
                    }
                    
                    setIsLoading(false);
                    console.log('✅ 應用程式初始化完成');
                    
                } catch (error) {
                    console.error('❌ 應用程式初始化失敗:', error);
                    setError(error.message);
                    setIsLoading(false);
                    
                    // 隱藏載入指示器
                    const loadingIndicator = document.getElementById('loadingIndicator');
                    if (loadingIndicator) {
                        loadingIndicator.classList.add('hidden');
                    }
                }
            };
            
            const initializeLIFF = async () => {
                try {
                    // 等待 LIFF SDK 載入
                    let attempts = 0;
                    while (typeof liff === 'undefined' && attempts < 50) {
                        await new Promise(resolve => setTimeout(resolve, 100));
                        attempts++;
                    }
                    
                    if (typeof liff === 'undefined') {
                        console.warn('⚠️ LIFF SDK 載入超時，使用一般模式');
                        setLiffReady(true);
                        return;
                    }
                    
                    console.log('📱 開始初始化 LIFF');
                    
                    // 這裡不初始化 LIFF，因為主頁不需要 LIFF 功能
                    // 只是確保 LIFF 環境下頁面能正常顯示
                    setLiffReady(true);
                    console.log('✅ LIFF 環境檢測完成');
                    
                } catch (error) {
                    console.warn('⚠️ LIFF 初始化失敗，繼續使用一般模式:', error);
                    setLiffReady(true);
                }
            };

            const handleCloseDeliveryNotice = () => {
                setShowDeliveryNotice(false);
                console.log('❌ 關閉配送提醒彈窗');
            };
            
            if (isLoading) {
                return null; // 載入指示器由 HTML 處理
            }
            
            if (error) {
                return (
                    <div className="min-h-screen flex items-center justify-center bg-gray-50">
                        <div className="text-center p-6">
                            <div className="text-red-500 text-6xl mb-4">⚠️</div>
                            <h2 className="text-xl font-bold mb-2">載入失敗</h2>
                            <p className="text-gray-600 mb-4">{error}</p>
                            <button 
                                onClick={() => window.location.reload()}
                                className="bg-red-500 text-white px-6 py-2 rounded-lg hover:bg-red-600"
                            >
                                重新載入
                            </button>
                        </div>
                    </div>
                );
            }

            if (currentPage === 'admin') {
                return <AdminDashboard />;
            }

            return (
                <div className="min-h-screen bg-gray-50">
                    <Header />
                    <Hero />
                    <BrandStory />
                    <Products />
                    <ProcessSection />
                    <CustomerReviews />
                    <MediaSection />
                    <Storage />
                    <OrderInstructions />
                    <OrderGuide />
                    <Footer />
                    <FloatingButtons />

                    {/* 配送提醒彈窗 */}
                    <DeliveryNoticeModal
                        isOpen={showDeliveryNotice}
                        onClose={handleCloseDeliveryNotice}
                    />
                </div>
            );
        }
        
        // 渲染應用程式 - 確保只有一個渲染調用
        ReactDOM.render(<App />, document.getElementById('root'));
    </script>
</body>

</html>